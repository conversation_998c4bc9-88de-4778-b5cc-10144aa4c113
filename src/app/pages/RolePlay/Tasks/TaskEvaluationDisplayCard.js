import React, {useState, useEffect, useRef, forwardRef, useImperativeHandle, useCallback} from 'react';
import {
  Card,
  Table,
  Typography,
  Switch,
  Alert,
  Tooltip,
  Button,
  Input,
  InputNumber,
  Popover,
  Form,
  message,
  Space,
  Modal,
} from 'antd';
import {
  QuestionCircleOutlined,
  DeleteOutlined,
  PlusOutlined,
  SaveOutlined,
  EditOutlined,
  RobotOutlined,
  LoadingOutlined,
} from '@ant-design/icons';
import {useTranslation} from 'react-i18next';
import './TaskEvaluationDisplayCard.scss';
import {
  createRoleplayTask,
  updateRoleplayTask,
  deleteRoleplayTask,
  createTasksFromPrompt,
} from '@src/app/services/RolePlay/TaskService';
import AntButton from '@src/app/component/AntButton';
import {BUTTON, API} from '@constant';
import { toast } from '@component/ToastProvider';
import TaskFormModal from './TaskFormModal';

const {Text, Paragraph} = Typography;
const {TextArea} = Input;



export const TaskEvaluationDisplayCard = forwardRef(({
  dataSource = [],
  onTaskAdd,
  onTaskUpdate,
  onTaskDelete,
  courseId, // Nhận courseId từ parent component
  hideButtons = false, // Prop để ẩn buttons
  ...restProps
}, ref) => {
  const {t} = useTranslation();

  const [isAIModalVisible, setIsAIModalVisible] = useState(false); // Trạng thái hiển thị modal tạo từ AI
  const [userPrompt, setUserPrompt] = useState(''); // Prompt người dùng nhập vào
  const [isGeneratingWithAI, setIsGeneratingWithAI] = useState(false); // Trạng thái đang tạo từ AI

  // State cho Task Form Modal
  const [isTaskModalVisible, setIsTaskModalVisible] = useState(false);
  const [editingTask, setEditingTask] = useState(null);
  const [isSubmittingTask, setIsSubmittingTask] = useState(false);


  // Expose methods to parent component
  useImperativeHandle(ref, () => ({
    showAIModal: () => setIsAIModalVisible(true),
    showTaskModal: () => {
      setEditingTask(null);
      setIsTaskModalVisible(true);
    },
  }));

  // Force re-render when dataSource changes to ensure UI reflects latest data
  const prevDataSourceRef = useRef();
  useEffect(() => {
    // Only trigger if dataSource actually changed (check both IDs and content)
    const currentSignature = dataSource.map(t => `${t._id}:${t.name}:${t.description}`).sort().join('|');
    const prevSignature = prevDataSourceRef.current?.map(t => `${t._id}:${t.name}:${t.description}`).sort().join('|') || '';

    if (currentSignature !== prevSignature) {
      // Only force re-render if we're not in the middle of a save operation
      setTimeout(() => {
        setForceRender(prev => prev + 1);
      }, 50);
    }

    prevDataSourceRef.current = dataSource;
  }, [dataSource]);






  const handleAddTask = () => {
    setEditingTask(null);
    setIsTaskModalVisible(true);
  };

  // Xử lý chỉnh sửa task
  const handleEditTask = (task) => {
    setEditingTask(task);
    setIsTaskModalVisible(true);
  };

  // Xử lý đóng modal task
  const handleTaskModalCancel = () => {
    setIsTaskModalVisible(false);
    setEditingTask(null);
  };

  // Xử lý submit form task
  const handleTaskSubmit = async (taskData) => {
    setIsSubmittingTask(true);
    try {
      if (editingTask) {
        // Chế độ chỉnh sửa
        const updatedTask = {...editingTask, ...taskData};
        if (onTaskUpdate) {
          onTaskUpdate(updatedTask);
        }
        toast.success(t('UPDATE_TASK_SUCCESS', 'Cập nhật nhiệm vụ thành công'));
      } else {
        // Chế độ thêm mới
        const newId = `temp_${Date.now()}`;
        const newTask = {
          _id: newId,
          ...taskData,
        };
        if (onTaskAdd) {
          onTaskAdd(newTask);
        }
        toast.success(t('ADD_TASK_SUCCESS', 'Thêm nhiệm vụ thành công'));
      }
      setIsTaskModalVisible(false);
      setEditingTask(null);
    } catch (error) {
      console.error('Error submitting task:', error);
      toast.error(t('TASK_SUBMIT_ERROR', 'Có lỗi xảy ra khi lưu nhiệm vụ'));
    } finally {
      setIsSubmittingTask(false);
    }
  };

  // Hiển thị modal nhập prompt AI
  const showAIModal = () => {
    setIsAIModalVisible(true);
    setUserPrompt('');
  };

  // Đóng modal nhập prompt AI
  const handleAIModalCancel = () => {
    setIsAIModalVisible(false);
    setUserPrompt('');
  };

  // Xử lý tạo task từ AI
  const handleGenerateWithAI = async () => {
    if (!courseId) {
      toast.error(t('COURSE_ID_REQUIRED', 'Course ID is required'));
      return;
    }

    // scenarioId không cần thiết vì model đã xóa field này

    if (!userPrompt.trim()) {
      toast.error(t('PROMPT_REQUIRED', 'Please enter a prompt'));
      return;
    }

    try {
      setIsGeneratingWithAI(true);
      const result = await createTasksFromPrompt(courseId, userPrompt.trim());

      // Xử lý nhiều định dạng response có thể từ AI
      let tasksArray = [];

      if (result && result.tasks && Array.isArray(result.tasks)) {
        tasksArray = result.tasks;
      } else if (result && Array.isArray(result)) {
        tasksArray = result;
      } else if (result && result.data && Array.isArray(result.data)) {
        tasksArray = result.data;
      } else if (result && result.data && result.data.tasks && Array.isArray(result.data.tasks)) {
        tasksArray = result.data.tasks;
      } else if (result && typeof result === 'object' && result.name) {
        tasksArray = [result];
      } else {
        // Thử tìm tasks trong các nested properties
        const findTasks = (obj, path = '') => {
          if (Array.isArray(obj)) {
            return obj;
          }
          if (obj && typeof obj === 'object') {
            for (const [key, value] of Object.entries(obj)) {
              if (key.toLowerCase().includes('task') && Array.isArray(value)) {
                return value;
              }
              const nested = findTasks(value, `${path}.${key}`);
              if (nested) return nested;
            }
          }
          return null;
        };

        const foundTasks = findTasks(result);
        if (foundTasks) {
          tasksArray = foundTasks;
        }
      }

      if (tasksArray.length > 0) {
        // Tính toán orderInScenario cho các task từ AI
        const currentTaskCount = dataSource.filter(task =>
          !task._id.toString().startsWith('temp_') // Chỉ đếm tasks đã được lưu
        ).length;

        // Thêm các task mới vào danh sách
        const newTasks = tasksArray.map((task, index) => {
          const newId = `temp_${Date.now()}_${index}`;
          const processedTask = {
            name: task.name || task.topic || task.title || `Task ${index + 1}`,
            description: task.description || task.desc || task.content || '',
            evaluationGuidelines: task.evaluationGuidelines || task.guidelines || task.criteria || task.evaluation || '',
            weight: parseInt(task.weight) || parseInt(task.score) || parseInt(task.points) || 0,
            exampleVideoUrl: task.exampleVideoUrl || task.videoUrl || task.video || '',
            helpfulLinks: task.helpfulLinks || task.links || task.resources || [],
            isMakeOrBreak: Boolean(task.isMakeOrBreak || task.critical || task.required),
            _id: newId,
            key: newId,
            orderInScenario: currentTaskCount + index + 1, // Thêm thứ tự cho task từ AI
          };

          return processedTask;
        });


        // Thêm các task mới vào state
        const newRowsToAdd = {};
        newTasks.forEach((task, index) => {
          newRowsToAdd[task._id] = task;
        });




        // Thay vì gọi onTaskAdd từng task riêng biệt, gọi một lần với tất cả tasks
        if (onTaskAdd && typeof onTaskAdd === 'function') {
          // Gọi onTaskAdd với một object chứa tất cả tasks
          onTaskAdd({
            type: 'BULK_ADD',
            tasks: newTasks
          });
        } else {
          // Fallback: gọi từng task riêng biệt nếu parent không hỗ trợ bulk add
          newTasks.forEach((task, index) => {
            if (onTaskAdd) onTaskAdd(task);
          });
        }

        toast.success(t('TASKS_GENERATED', `Đã tạo thành công ${newTasks.length} nhiệm vụ từ AI`));
        setIsAIModalVisible(false);
        setUserPrompt(''); // Reset prompt
      } else {
        toast.error(t('GENERATE_ERROR', 'AI không trả về nhiệm vụ hợp lệ. Vui lòng thử lại với prompt khác.'));
      }
    } catch (error) {
      toast.error(t('GENERATE_ERROR', 'Không thể tạo nhiệm vụ từ AI. Vui lòng thử lại.'));
    } finally {
      setIsGeneratingWithAI(false);
    }
  };

  const handleDeleteTask = async taskId => {
    try {
      // Nếu là task tạm thời (chưa lưu vào DB)
      if (taskId.toString().startsWith('temp_')) {
        onTaskDelete(taskId);
        return;
      }

      // Hiển thị modal xác nhận xóa
      Modal.confirm({
        title: t('CONFIRM_DELETE_TASK', 'Are you sure you want to delete this task?'),
        content: t('DELETE_TASK_WARNING', 'This action cannot be undone.'),
        okText: t('DELETE', 'Delete'),
        okType: 'danger',
        cancelText: t('CANCEL', 'Cancel'),
        onOk: async () => {
          try {
            await deleteRoleplayTask(taskId);
            toast.success(t('TASK_DELETED', 'Task deleted successfully'));
            onTaskDelete(taskId);
          } catch (error) {
            toast.error(t('DELETE_ERROR', 'Failed to delete task'));
          }
        },
      });
    } catch (error) {
      console.error('Error in handleDeleteTask:', error);
      toast.error(t('DELETE_ERROR', 'Failed to delete task'));
    }
  };

  const columns = [
    {
      title: (
        <Tooltip title={t('TASK_TOPIC_TOOLTIP', 'The main subject.')}>
          <Text strong>{t('TOPIC_COLUMN', 'Topic')}</Text> <QuestionCircleOutlined />
        </Tooltip>
      ),
      dataIndex: 'name',
      key: 'name',
      width: '12%',
      render: text =>
        text ? (
          <Paragraph style={{whiteSpace: 'pre-line', marginBottom: 0, lineHeight: '1.5'}}>{text}</Paragraph>
        ) : (
          <Text type="secondary" italic>{t('NO_TOPIC', 'Chưa có chủ đề')}</Text>
        ),
    },
    {
      title: (
        <Tooltip title={t('EVALUATION_GUIDELINES_TOOLTIP', 'Criteria for evaluation.')}>
          <Text strong>{t('EVALUATION_GUIDELINES_COLUMN', 'Evaluation guidelines')}</Text> <QuestionCircleOutlined />
        </Tooltip>
      ),
      dataIndex: 'evaluationGuidelines',
      key: 'evaluationGuidelines',
      width: '24%',
      render: text =>
        text ? (
          <Paragraph style={{whiteSpace: 'pre-line', marginBottom: 0, lineHeight: '1.5'}}>{text}</Paragraph>
        ) : (
          <Text type="secondary" italic>{t('NO_EVALUATION_GUIDELINES', 'Chưa có hướng dẫn đánh giá')}</Text>
        ),
    },
    {
      title: (
        <Tooltip title={t('WEIGHT_TOOLTIP', 'Importance percentage.')}>
          <Text strong>{t('WEIGHT_COLUMN', 'Weight')}</Text> <QuestionCircleOutlined />
        </Tooltip>
      ),
      dataIndex: 'weight',
      key: 'weight',
      width: '8%',
      align: 'center',
      render: (text) => (
        <Text>{text || 0}%</Text>
      ),
    },
    {
      title: (
        <Tooltip title={t('EXAMPLE_VIDEOS_TOOLTIP', 'Link to example video.')}>
          <Text strong>{t('EXAMPLE_VIDEOS_COLUMN', 'Example videos')}</Text> <QuestionCircleOutlined />
        </Tooltip>
      ),
      dataIndex: 'exampleVideoUrl',
      key: 'exampleVideoUrl',
      width: '12%',
      render: text =>
        text ? (
          <Paragraph style={{whiteSpace: 'pre-line', marginBottom: 0, lineHeight: '1.5'}}>{text}</Paragraph>
        ) : (
          <Text type="secondary" italic>{t('NO_EXAMPLE_VIDEO', 'Chưa có video mẫu')}</Text>
        ),
    },
    {
      title: (
        <Tooltip title={t('HELPFUL_LINKS_TOOLTIP', 'Link to helpful resources. Separate multiple links with commas.')}>
          <Text strong>{t('HELPFUL_LINKS_COLUMN', 'Helpful links')}</Text> <QuestionCircleOutlined />
        </Tooltip>
      ),
      dataIndex: 'helpfulLinks',
      key: 'helpfulLinks',
      width: '12%',
      render: links => {
        if (!links || (Array.isArray(links) && links.length === 0)) {
          return (
            <Text type="secondary" italic>
              {t('NO_HELPFUL_LINKS', 'Chưa có liên kết hữu ích')}
            </Text>
          );
        }
        return Array.isArray(links) ? links.join(', ') : links;
      },
    },
    {
      title: (
        <Tooltip title={t('MAKE_OR_BREAK_TOOLTIP', 'Critical for passing.')}>
          <Text strong>{t('MAKE_OR_BREAK_COLUMN', 'Make or Break')}</Text> <QuestionCircleOutlined />
        </Tooltip>
      ),
      dataIndex: 'isMakeOrBreak',
      key: 'isMakeOrBreak',
      width: '8%',
      align: 'center',
      render: (isMakeOrBreak) => (
        <Switch
          checked={isMakeOrBreak}
          disabled
        />
      ),
    },
    {
      title: t('ACTIONS_COLUMN', 'Actions'),
      key: 'actions',
      width: '10%',
      align: 'center',
      render: (_, record) => {
        return (
          <div className="action-buttons">
            <Tooltip title={t('EDIT_TASK_TOOLTIP', 'Chỉnh sửa nhiệm vụ')}>
              <div
                className="action-btn action-btn-edit"
                onClick={() => handleEditTask(record)}
              >
                <EditOutlined />
              </div>
            </Tooltip>
            <Tooltip title={t('DELETE_TASK_TOOLTIP', 'Xóa nhiệm vụ này')}>
              <div
                className="action-btn action-btn-delete"
                onClick={() => handleDeleteTask(record._id)}
              >
                <DeleteOutlined />
              </div>
            </Tooltip>
          </div>
        );
      },
    },
  ];





  return (
    <div className="task-evaluation-display-card" style={restProps.style}>
      {!hideButtons && (
        <div style={{
          padding: '16px',
          borderBottom: '1px solid #f0f0f0',
          backgroundColor: '#fafafa'
        }}>
          <Space style={{justifyContent: 'space-between', width: '100%'}}>
            <AntButton onClick={handleAddTask} size={'large'} icon={<PlusOutlined />} type={BUTTON.DEEP_NAVY}>
              {t('ADD_TASK_TOPIC', 'Add Topic / Task')}
            </AntButton>
            <AntButton
              onClick={showAIModal}
              icon={<RobotOutlined />}
              disabled={!courseId}
              type={'primary'}
              style={{background: '#722ED1'}}
              size={'large'}
              data-testid="ai-create-button"
            >
              {t('CREATE_FROM_AI', 'Create from AI')}
            </AntButton>
          </Space>
        </div>
      )}

      {/* Modal nhập prompt AI */}
      <Modal
        title={t('CREATE_TASKS_FROM_AI', 'Create Tasks from AI')}
        open={isAIModalVisible}
        onCancel={handleAIModalCancel}
        footer={[
          <AntButton key="cancel" onClick={handleAIModalCancel} size={'large'}>
            {t('CANCEL', 'Cancel')}
          </AntButton>,
          <AntButton
            key="generate"
            type="primary"
            loading={isGeneratingWithAI}
            onClick={handleGenerateWithAI}
            style={{background: '#722ED1'}}
            size={'large'}
          >
            {t('GENERATE', 'Generate')}
          </AntButton>,
        ]}
      >
        <Form layout="vertical">
          <Form.Item
            label={t('PROMPT_LABEL', 'Describe the tasks you want to generate')}
            help={t('PROMPT_HELP', 'For example: "conversation between a student and a potential customer"')}
          >
            <TextArea
              rows={4}
              value={userPrompt}
              onChange={e => setUserPrompt(e.target.value)}
              placeholder={t('PROMPT_PLACEHOLDER', 'Enter your prompt here...')}
            />
          </Form.Item>
        </Form>
      </Modal>

      <div className="table-container">
        <Table
          rowClassName="task-row"
          columns={columns}
          dataSource={dataSource.map(task => ({...task, key: task._id}))}
          pagination={false}
          className="evaluation-table"
          bordered
        />
      </div>



      {/* Task Form Modal */}
      <TaskFormModal
        visible={isTaskModalVisible}
        onCancel={handleTaskModalCancel}
        onSubmit={handleTaskSubmit}
        initialData={editingTask}
        loading={isSubmittingTask}
      />
    </div>
  );
});

TaskEvaluationDisplayCard.displayName = 'TaskEvaluationDisplayCard';
